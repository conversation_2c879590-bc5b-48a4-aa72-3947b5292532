#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for validating OA to Feishu automation setup
"""

import json
import sys
from pathlib import Path

from config.settings import settings
from core.cookie_manager import CookieManager
from core.feishu_client import FeishuClient
from utils.logger import setup_logger

def test_cookiecloud_connection():
    """Test CookieCloud connection and cookie retrieval"""
    print("\n" + "="*60)
    print("🍪 Testing CookieCloud Connection")
    print("="*60)
    
    try:
        cookie_manager = CookieManager()
        
        # Test connection
        if cookie_manager.load_cookies():
            print("✅ CookieCloud connection successful")
            
            # Get cookie info
            info = cookie_manager.get_cookies_info()
            print(f"📊 Cookie Statistics:")
            print(f"   - Total domains: {info['total_domains']}")
            print(f"   - Total cookies: {info['total_cookies']}")
            print(f"   - Last update: {info['update_time']}")
            
            # Test OA cookies specifically
            oa_cookies = cookie_manager.get_oa_cookies()
            print(f"🔑 OA Cookies: {len(oa_cookies)} found")
            
            if cookie_manager.validate_oa_cookies():
                print("✅ OA cookies validation passed")
                return True
            else:
                print("❌ OA cookies validation failed")
                return False
        else:
            print("❌ CookieCloud connection failed")
            return False
            
    except Exception as e:
        print(f"❌ CookieCloud test failed: {e}")
        return False

def test_feishu_connection():
    """Test Feishu API connection and permissions"""
    print("\n" + "="*60)
    print("🚀 Testing Feishu Connection")
    print("="*60)
    
    try:
        feishu_client = FeishuClient()
        
        # Test access token
        access_token = feishu_client.get_access_token()
        if access_token:
            print("✅ Feishu access token obtained successfully")
            print(f"🔑 Token: {access_token[:20]}...")
        else:
            print("❌ Failed to get Feishu access token")
            return False
        
        # Test table access
        print("\n📋 Testing table access...")
        
        # Contract table
        try:
            contract_fields = feishu_client.get_table_fields(settings.feishu.contract_table_id)
            print(f"✅ Contract table: {len(contract_fields)} fields found")
        except Exception as e:
            print(f"❌ Contract table access failed: {e}")
        
        # Payment table
        try:
            payment_fields = feishu_client.get_table_fields(settings.feishu.payment_table_id)
            print(f"✅ Payment table: {len(payment_fields)} fields found")
        except Exception as e:
            print(f"❌ Payment table access failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Feishu test failed: {e}")
        return False

def test_oa_connection():
    """Test OA system connection"""
    print("\n" + "="*60)
    print("🏢 Testing OA Connection")
    print("="*60)
    
    try:
        cookie_manager = CookieManager()
        if not cookie_manager.load_cookies():
            print("❌ Cannot test OA - CookieCloud failed")
            return False
        
        from core.oa_client import OAClient
        oa_client = OAClient(cookie_manager)
        
        print("✅ OA client initialized successfully")
        print(f"🌐 Base URL: {settings.oa.base_url}")
        print(f"🍪 Session cookies: {len(oa_client.session.cookies)} cookies loaded")
        
        # Note: We don't actually test API calls here to avoid unnecessary requests
        print("ℹ️  OA API calls will be tested during actual execution")
        
        return True
        
    except Exception as e:
        print(f"❌ OA test failed: {e}")
        return False

def display_configuration():
    """Display current configuration"""
    print("\n" + "="*60)
    print("⚙️  Configuration Summary")
    print("="*60)
    
    print("🍪 CookieCloud:")
    print(f"   Server: {settings.cookiecloud.server_url}")
    print(f"   UUID: {settings.cookiecloud.uuid}")
    print(f"   Password: {'*' * len(settings.cookiecloud.password)}")
    
    print("\n🚀 Feishu:")
    print(f"   App ID: {settings.feishu.app_id}")
    print(f"   App Secret: {'*' * len(settings.feishu.app_secret)}")
    print(f"   Base ID: {settings.feishu.base_id}")
    print(f"   Contract Table: {settings.feishu.contract_table_id}")
    print(f"   Payment Table: {settings.feishu.payment_table_id}")
    
    print("\n🏢 OA:")
    print(f"   Base URL: {settings.oa.base_url}")
    print(f"   Domain: {settings.oa.domain}")
    print(f"   Timeout: {settings.oa.timeout}s")

def get_feishu_table_schemas():
    """Get and display Feishu table schemas"""
    print("\n" + "="*60)
    print("📋 Feishu Table Schemas")
    print("="*60)
    
    try:
        feishu_client = FeishuClient()
        
        # Contract table schema
        print("\n📄 Contract Table Schema:")
        contract_schema = feishu_client.get_table_schema(settings.feishu.contract_table_id)
        if contract_schema:
            print(f"   Table ID: {settings.feishu.contract_table_id}")
            print(f"   Fields: {len(contract_schema['fields'])}")
            for field in contract_schema['fields'][:10]:  # Show first 10 fields
                print(f"     - {field['field_name']} (Type: {field['field_type']})")
            if len(contract_schema['fields']) > 10:
                print(f"     ... and {len(contract_schema['fields']) - 10} more fields")
        
        # Payment table schema
        print("\n💰 Payment Table Schema:")
        payment_schema = feishu_client.get_table_schema(settings.feishu.payment_table_id)
        if payment_schema:
            print(f"   Table ID: {settings.feishu.payment_table_id}")
            print(f"   Fields: {len(payment_schema['fields'])}")
            for field in payment_schema['fields'][:10]:  # Show first 10 fields
                print(f"     - {field['field_name']} (Type: {field['field_type']})")
            if len(payment_schema['fields']) > 10:
                print(f"     ... and {len(payment_schema['fields']) - 10} more fields")
        
        # Save schemas to file for reference
        schemas_file = Path("feishu_schemas.json")
        with open(schemas_file, 'w', encoding='utf-8') as f:
            json.dump({
                "contract_table": contract_schema,
                "payment_table": payment_schema
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 Schemas saved to: {schemas_file}")
        
    except Exception as e:
        print(f"❌ Failed to get table schemas: {e}")

def main():
    """Main test function"""
    print("🧪 OA to Feishu Automation - Setup Test")
    print("="*60)
    
    # Setup logging
    setup_logger(level="INFO")
    
    # Display configuration
    display_configuration()
    
    # Run tests
    tests = [
        ("CookieCloud", test_cookiecloud_connection),
        ("Feishu", test_feishu_connection),
        ("OA", test_oa_connection),
    ]
    
    results = {}
    for test_name, test_func in tests:
        results[test_name] = test_func()
    
    # Get table schemas if Feishu test passed
    if results.get("Feishu"):
        get_feishu_table_schemas()
    
    # Summary
    print("\n" + "="*60)
    print("📊 Test Results Summary")
    print("="*60)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name:15} {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "="*60)
    if all_passed:
        print("🎉 All tests passed! You're ready to run the automation.")
        print("💡 Next step: python main.py --validate-only")
    else:
        print("⚠️  Some tests failed. Please check your configuration.")
        print("📖 Refer to README.md for troubleshooting guide.")
    print("="*60)
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
