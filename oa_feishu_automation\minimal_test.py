#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Minimal test to check basic Python functionality
"""

print("🧪 Minimal Test Starting...")

try:
    import sys
    print(f"✅ Python version: {sys.version}")
    
    import os
    print(f"✅ Current directory: {os.getcwd()}")
    
    from pathlib import Path
    print(f"✅ Pathlib imported")
    
    # Test if .env file exists
    env_file = Path(".env")
    if env_file.exists():
        print("✅ .env file found")
    else:
        print("⚠️  .env file not found")
    
    # Test basic imports
    import json
    import time
    import requests
    print("✅ Basic packages imported")
    
    print("🎉 Minimal test completed successfully!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
