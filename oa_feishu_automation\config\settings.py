#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration settings for OA to Feishu automation
"""

import os
from dotenv import load_dotenv
from dataclasses import dataclass
from typing import Optional, Dict, Any

# Load environment variables
load_dotenv()

@dataclass
class CookieCloudConfig:
    """CookieCloud configuration"""
    server_url: str
    uuid: str
    password: str

@dataclass
class FeishuConfig:
    """Feishu configuration"""
    app_id: str
    app_secret: str
    base_id: str
    contract_table_id: str
    payment_table_id: str
    auth_url: str = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/"
    base_url: str = "https://open.feishu.cn/open-apis/"

@dataclass
class OAConfig:
    """OA system configuration"""
    base_url: str
    domain: str
    timeout: int = 30
    max_retries: int = 3
    retry_interval: int = 2

@dataclass
class AppConfig:
    """Application configuration"""
    log_level: str = "INFO"
    date_range_days: int = 30
    
class Settings:
    """Application settings manager"""
    
    def __init__(self):
        self._validate_env_vars()
        
        self.cookiecloud = CookieCloudConfig(
            server_url=os.getenv('COOKIECLOUD_SERVER_URL'),
            uuid=os.getenv('COOKIECLOUD_UUID'),
            password=os.getenv('COOKIECLOUD_PASSWORD')
        )
        
        self.feishu = FeishuConfig(
            app_id=os.getenv('FEISHU_APP_ID'),
            app_secret=os.getenv('FEISHU_APP_SECRET'),
            base_id=os.getenv('FEISHU_BASE_ID'),
            contract_table_id=os.getenv('FEISHU_CONTRACT_TABLE_ID'),
            payment_table_id=os.getenv('FEISHU_PAYMENT_TABLE_ID')
        )
        
        self.oa = OAConfig(
            base_url=os.getenv('OA_BASE_URL', 'http://oa.yaduo.com'),
            domain=os.getenv('OA_DOMAIN', 'oa.yaduo.com'),
            timeout=int(os.getenv('TIMEOUT', '30')),
            max_retries=int(os.getenv('MAX_RETRIES', '3')),
            retry_interval=int(os.getenv('RETRY_INTERVAL', '2'))
        )
        
        self.app = AppConfig(
            log_level=os.getenv('LOG_LEVEL', 'INFO'),
            date_range_days=int(os.getenv('DATE_RANGE_DAYS', '30'))
        )
    
    def _validate_env_vars(self):
        """Validate required environment variables"""
        required_vars = [
            'COOKIECLOUD_SERVER_URL',
            'COOKIECLOUD_UUID', 
            'COOKIECLOUD_PASSWORD',
            'FEISHU_APP_ID',
            'FEISHU_APP_SECRET',
            'FEISHU_BASE_ID',
            'FEISHU_CONTRACT_TABLE_ID',
            'FEISHU_PAYMENT_TABLE_ID'
        ]
        
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

# Global settings instance
settings = Settings()

# OA Report configurations
OA_REPORTS = {
    'contract': {
        'report_id': 142,
        'name': '合同用印审批',
        'field_prefix': 'field8287',
        'template_id': 914,
        'condition_field': '8287,-11,-12'
    },
    'payment': {
        'report_id': 215,
        'name': '付款申请单',
        'field_prefix': 'field6340',
        'template_id': 912,
        'condition_field': '6340,-11,-12'
    },
    'prepayment': {
        'report_id': 216,
        'name': '预付款申请单',
        'field_prefix': 'field15058',
        'template_id': 913,
        'condition_field': '15058,-11,-12'
    }
}

# Company department codes (from API requests)
COMPANY_CODES = "25,66,56,73,53,52,51,50,67,65,47,46,45,44,43,42,41,40,39,38,64,74,37,36,35,34,33,32,31,82,30,110,75,68,112,77,71,70,28,63,113,81,80,78,72,122,102,79,26,123,128,88,136,89,137,90,135,91,138,92,93,95,94,96,97,98,99,100,84,85,83,87,86,103,101,106,105,107,108,111,118,120,124,127,117,130,125,139"
