#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Deployment and setup script for OA to Feishu automation
"""

import os
import sys
import subprocess
from pathlib import Path
import shutil

def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("\n📦 Installing dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def setup_directories():
    """Create necessary directories"""
    print("\n📁 Setting up directories...")
    
    directories = ["downloads", "logs", "backups"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    return True

def setup_environment_file():
    """Setup environment configuration file"""
    print("\n⚙️  Setting up environment configuration...")
    
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if not env_example.exists():
        print("❌ .env.example file not found")
        return False
    
    if not env_file.exists():
        shutil.copy(env_example, env_file)
        print("✅ Created .env file from template")
        print("⚠️  Please edit .env file with your actual configuration")
        return True
    else:
        print("ℹ️  .env file already exists")
        return True

def run_initial_tests():
    """Run initial setup tests"""
    print("\n🧪 Running initial tests...")
    
    try:
        # Check if we can import main modules
        from config.settings import settings
        from core.cookie_manager import CookieManager
        from core.feishu_client import FeishuClient
        print("✅ All modules imported successfully")
        
        # Try to run test setup
        result = subprocess.run([
            sys.executable, "test_setup.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Setup tests passed")
            return True
        else:
            print("⚠️  Setup tests failed - please check configuration")
            print("📋 Test output:")
            print(result.stdout)
            if result.stderr:
                print("❌ Errors:")
                print(result.stderr)
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def create_run_scripts():
    """Create convenient run scripts"""
    print("\n📝 Creating run scripts...")
    
    # Windows batch script
    batch_script = """@echo off
echo Starting OA to Feishu Automation...
python main.py %*
pause
"""
    
    with open("run.bat", "w") as f:
        f.write(batch_script)
    print("✅ Created run.bat for Windows")
    
    # Unix shell script
    shell_script = """#!/bin/bash
echo "Starting OA to Feishu Automation..."
python3 main.py "$@"
"""
    
    with open("run.sh", "w") as f:
        f.write(shell_script)
    
    # Make shell script executable
    try:
        os.chmod("run.sh", 0o755)
        print("✅ Created run.sh for Unix/Linux")
    except:
        print("⚠️  Created run.sh (may need to make executable manually)")
    
    return True

def display_next_steps():
    """Display next steps for user"""
    print("\n" + "="*60)
    print("🎉 Deployment completed!")
    print("="*60)
    
    print("\n📋 Next Steps:")
    print("1. Edit .env file with your actual configuration:")
    print("   - CookieCloud server URL, UUID, and password")
    print("   - Feishu App ID and App Secret")
    print("   - Feishu Base ID and Table IDs")
    
    print("\n2. Test your configuration:")
    print("   python test_setup.py")
    
    print("\n3. Analyze Excel reference files (optional):")
    print("   python analyze_excel.py")
    
    print("\n4. Run the automation:")
    print("   python main.py --validate-only  # Validate setup")
    print("   python main.py --get-schema     # Get table schemas")
    print("   python main.py                 # Run full automation")
    
    print("\n5. Use convenient run scripts:")
    print("   Windows: run.bat")
    print("   Unix/Linux: ./run.sh")
    
    print("\n📚 Documentation:")
    print("   - README.md for detailed usage instructions")
    print("   - logs/ directory for application logs")
    
    print("\n🔧 Troubleshooting:")
    print("   - Check logs for detailed error information")
    print("   - Verify all environment variables are set correctly")
    print("   - Ensure CookieCloud is running and accessible")
    print("   - Confirm Feishu app permissions are properly configured")

def main():
    """Main deployment function"""
    print("🚀 OA to Feishu Automation - Deployment Script")
    print("="*60)
    
    steps = [
        ("Python Version", check_python_version),
        ("Dependencies", install_dependencies),
        ("Directories", setup_directories),
        ("Environment", setup_environment_file),
        ("Run Scripts", create_run_scripts),
    ]
    
    all_success = True
    
    for step_name, step_func in steps:
        if not step_func():
            all_success = False
            print(f"❌ {step_name} setup failed")
            break
        print(f"✅ {step_name} setup completed")
    
    if all_success:
        print("\n🧪 Running initial validation...")
        test_success = run_initial_tests()
        
        display_next_steps()
        
        if test_success:
            print("\n🎉 All setup completed successfully!")
            return 0
        else:
            print("\n⚠️  Setup completed but tests failed.")
            print("Please check your configuration and try again.")
            return 1
    else:
        print("\n❌ Deployment failed. Please fix the issues and try again.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
