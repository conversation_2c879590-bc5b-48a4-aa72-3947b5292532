#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Field mappings for OA to Feishu data transformation
"""

# Contract field mapping (合同用印审批)
CONTRACT_FIELD_MAPPING = {
    # Add mappings based on Excel structure analysis
    # This will be populated after examining the Excel files
    'workflow_id': '流程ID',
    'request_id': '申请编号',
    'request_date': '申请日期',
    'applicant': '申请人',
    'department': '申请部门',
    'contract_name': '合同名称',
    'contract_type': '合同类型',
    'contract_amount': '合同金额',
    'counterparty': '合同相对方',
    'status': '审批状态',
    'approval_date': '审批日期',
    'remarks': '备注'
}

# Payment application field mapping (付款申请单)
PAYMENT_FIELD_MAPPING = {
    'workflow_id': '流程ID',
    'request_id': '申请编号',
    'request_date': '申请日期',
    'applicant': '申请人',
    'department': '申请部门',
    'payment_type': '付款类型',
    'payment_amount': '付款金额',
    'payee': '收款方',
    'payment_purpose': '付款用途',
    'status': '审批状态',
    'approval_date': '审批日期',
    'payment_date': '付款日期',
    'remarks': '备注'
}

# Prepayment application field mapping (预付款申请单)
PREPAYMENT_FIELD_MAPPING = {
    'workflow_id': '流程ID',
    'request_id': '申请编号',
    'request_date': '申请日期',
    'applicant': '申请人',
    'department': '申请部门',
    'prepayment_type': '预付款类型',
    'prepayment_amount': '预付款金额',
    'payee': '收款方',
    'prepayment_purpose': '预付款用途',
    'status': '审批状态',
    'approval_date': '审批日期',
    'expected_payment_date': '预计付款日期',
    'remarks': '备注'
}

# Mapping configuration for each report type
FIELD_MAPPINGS = {
    'contract': CONTRACT_FIELD_MAPPING,
    'payment': PAYMENT_FIELD_MAPPING,
    'prepayment': PREPAYMENT_FIELD_MAPPING
}

# Common field types for Feishu
FEISHU_FIELD_TYPES = {
    'text': 1,          # 文本
    'number': 2,        # 数字
    'select': 3,        # 单选
    'multi_select': 4,  # 多选
    'date': 5,          # 日期
    'checkbox': 7,      # 复选框
    'user': 11,         # 人员
    'phone': 13,        # 电话号码
    'url': 15,          # 超链接
    'attachment': 17,   # 附件
    'barcode': 20,      # 条码
    'progress': 21,     # 进度
    'currency': 22,     # 货币
    'rating': 23,       # 评分
    'created_time': 1001,  # 创建时间
    'modified_time': 1002, # 最后更新时间
    'created_user': 1003,  # 创建人
    'modified_user': 1004  # 修改人
}
