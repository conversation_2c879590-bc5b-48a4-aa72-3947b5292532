#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OA system client for fetching reports and downloading Excel files
"""

import time
import requests
from typing import Dict, Optional, <PERSON><PERSON>, Any
from dataclasses import dataclass
from pathlib import Path
import urllib.parse

from ..config.settings import settings, OA_REPORTS, COMPANY_CODES
from ..utils.logger import logger
from ..utils.date_utils import get_date_range, get_current_timestamp
from .cookie_manager import CookieManager

@dataclass
class ExportTaskInfo:
    """Export task information"""
    status: int
    percent: str
    task_id: Optional[int]
    file_id: Optional[str]
    completed: bool
    message: Optional[str] = None

class OAClient:
    """
    OA system client for report data fetching and Excel downloads
    """
    
    def __init__(self, cookie_manager: <PERSON><PERSON>anager):
        """
        Initialize OA client
        
        Args:
            cookie_manager: CookieManager instance for authentication
        """
        self.config = settings.oa
        self.cookie_manager = cookie_manager
        self.session = requests.Session()
        self.session.timeout = self.config.timeout
        self._setup_session()
    
    def _setup_session(self):
        """Setup session with headers and cookies"""
        # Get cookies from CookieManager
        cookies = self.cookie_manager.get_oa_cookies()
        self.session.cookies.update(cookies)
        
        # Set default headers
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'en,zh-CN;q=0.9,zh;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'DNT': '1',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': f'{self.config.base_url}/spa/workflow/static/index.html'
        })
        
        logger.info("OA session setup completed")
    
    def refresh_cookies(self):
        """Refresh session cookies"""
        if self.cookie_manager.refresh_cookies():
            cookies = self.cookie_manager.get_oa_cookies()
            self.session.cookies.clear()
            self.session.cookies.update(cookies)
            logger.info("OA session cookies refreshed")
        else:
            logger.error("Failed to refresh OA cookies")
    
    def get_report_data(self, report_type: str, date_range_days: int = 30) -> Optional[str]:
        """
        Step 1: Get report data with filter conditions
        
        Args:
            report_type: Type of report ('contract', 'payment', 'prepayment')
            date_range_days: Number of days to look back
            
        Returns:
            sessionkey string if successful, None if failed
        """
        if report_type not in OA_REPORTS:
            logger.error(f"Unknown report type: {report_type}")
            return None
        
        report_config = OA_REPORTS[report_type]
        start_date, end_date = get_date_range(date_range_days)
        
        url = f"{self.config.base_url}/api/workflow/standCustomReport/getReportData"
        
        # Build form data based on the API request patterns
        form_data = {
            f"{report_config['field_prefix']}_opt1": "2",
            f"{report_config['field_prefix']}_value1": COMPANY_CODES,
            "field-11_opt1": "6",
            "field-11_value1": start_date,
            "field-11_value2": end_date,
            "field-12_value1": "1108" if report_type == "payment" else ("1106" if report_type == "contract" else "1107"),
            "conditionfieldids": report_config['condition_field'],
            "templateid": str(report_config['template_id']),
            "reportParamsKey": f"{report_config['report_id']}{get_current_timestamp()}",
            "reportid": str(report_config['report_id'])
        }
        
        try:
            logger.info(f"Requesting {report_config['name']} data for date range {start_date} to {end_date}")
            
            response = self.session.post(
                url,
                data=form_data,
                headers={'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8'}
            )
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('success') and 'sessionkey' in result:
                        sessionkey = result['sessionkey']
                        logger.info(f"Successfully got report data, sessionkey: {sessionkey}")
                        return sessionkey
                    else:
                        logger.error(f"API returned error: {result}")
                        return None
                except ValueError as e:
                    logger.error(f"Failed to parse JSON response: {e}")
                    return None
            else:
                logger.error(f"HTTP error {response.status_code}: {response.text}")
                return None
                
        except requests.RequestException as e:
            logger.error(f"Network error while getting report data: {e}")
            return None
    
    def export_excel(self, report_type: str, sessionkey: str) -> bool:
        """
        Step 2: Request Excel export
        
        Args:
            report_type: Type of report
            sessionkey: Session key from get_report_data
            
        Returns:
            True if export request successful
        """
        if report_type not in OA_REPORTS:
            logger.error(f"Unknown report type: {report_type}")
            return False
        
        report_config = OA_REPORTS[report_type]
        url = f"{self.config.base_url}/api/workflow/standCustomReport/exportExcel"
        
        form_data = {
            'sessionkey': sessionkey,
            'reportid': str(report_config['report_id']),
            'method': 'exportExcel',
            '__random__': str(get_current_timestamp())
        }
        
        try:
            logger.info(f"Requesting Excel export for {report_config['name']}")
            
            response = self.session.post(
                url,
                data=form_data,
                headers={'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8'}
            )
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('success'):
                        logger.info("Excel export request successful")
                        return True
                    else:
                        logger.error(f"Export request failed: {result}")
                        return False
                except ValueError as e:
                    logger.error(f"Failed to parse export response: {e}")
                    return False
            else:
                logger.error(f"HTTP error during export: {response.status_code}")
                return False
                
        except requests.RequestException as e:
            logger.error(f"Network error during Excel export: {e}")
            return False

    def check_export_status(self, report_type: str) -> Optional[ExportTaskInfo]:
        """
        Step 3: Check export task status

        Args:
            report_type: Type of report

        Returns:
            ExportTaskInfo if successful, None if failed
        """
        if report_type not in OA_REPORTS:
            logger.error(f"Unknown report type: {report_type}")
            return None

        report_config = OA_REPORTS[report_type]
        url = f"{self.config.base_url}/api/workflow/standCustomReport/exportTask"

        params = {
            'reportid': str(report_config['report_id']),
            'method': 'list',
            '__random__': str(get_current_timestamp())
        }

        try:
            response = self.session.get(url, params=params)

            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('success') and 'data' in result:
                        data = result['data']
                        if data and len(data) > 0:
                            task = data[0]  # Get the latest task

                            return ExportTaskInfo(
                                status=task.get('status', 0),
                                percent=task.get('percent', '0%'),
                                task_id=task.get('id'),
                                file_id=task.get('fileid'),
                                completed=task.get('status') == 2,
                                message=task.get('message')
                            )
                        else:
                            logger.warning("No export tasks found")
                            return None
                    else:
                        logger.error(f"Failed to get export status: {result}")
                        return None
                except ValueError as e:
                    logger.error(f"Failed to parse status response: {e}")
                    return None
            else:
                logger.error(f"HTTP error checking status: {response.status_code}")
                return None

        except requests.RequestException as e:
            logger.error(f"Network error checking export status: {e}")
            return None

    def download_excel_file(self, file_id: str, filename: str, output_dir: str = "downloads") -> Optional[Path]:
        """
        Step 4: Download the exported Excel file

        Args:
            file_id: File ID from export task
            filename: Desired filename
            output_dir: Output directory

        Returns:
            Path to downloaded file if successful, None if failed
        """
        url = f"{self.config.base_url}/api/workflow/standCustomReport/downloadExcel"

        params = {
            'fileid': file_id,
            '__random__': str(get_current_timestamp())
        }

        try:
            logger.info(f"Downloading Excel file: {filename}")

            response = self.session.get(url, params=params, stream=True)

            if response.status_code == 200:
                # Create output directory
                output_path = Path(output_dir)
                output_path.mkdir(parents=True, exist_ok=True)

                # Generate filename with timestamp
                timestamp = time.strftime("%Y%m%d%H%M%S")
                file_path = output_path / f"{filename}-{timestamp}.xlsx"

                # Download file
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)

                logger.info(f"Excel file downloaded successfully: {file_path}")
                return file_path
            else:
                logger.error(f"HTTP error downloading file: {response.status_code}")
                return None

        except requests.RequestException as e:
            logger.error(f"Network error downloading Excel file: {e}")
            return None
        except IOError as e:
            logger.error(f"File I/O error: {e}")
            return None

    def full_download_process(self, report_type: str, output_dir: str = "downloads",
                            date_range_days: int = 30, max_wait_time: int = 300) -> Optional[Path]:
        """
        Complete process: get data, export, wait for completion, and download

        Args:
            report_type: Type of report to download
            output_dir: Output directory for downloaded files
            date_range_days: Number of days to look back
            max_wait_time: Maximum time to wait for export completion (seconds)

        Returns:
            Path to downloaded file if successful, None if failed
        """
        if report_type not in OA_REPORTS:
            logger.error(f"Unknown report type: {report_type}")
            return None

        report_config = OA_REPORTS[report_type]
        logger.info(f"Starting full download process for {report_config['name']}")

        # Step 1: Get report data
        sessionkey = self.get_report_data(report_type, date_range_days)
        if not sessionkey:
            logger.error("Failed to get report data")
            return None

        # Step 2: Request Excel export
        if not self.export_excel(report_type, sessionkey):
            logger.error("Failed to request Excel export")
            return None

        # Step 3: Wait for export completion
        start_time = time.time()
        while time.time() - start_time < max_wait_time:
            task_info = self.check_export_status(report_type)

            if task_info:
                logger.info(f"Export progress: {task_info.percent}")

                if task_info.completed and task_info.file_id:
                    logger.info("Export completed successfully")

                    # Step 4: Download file
                    return self.download_excel_file(
                        task_info.file_id,
                        report_config['name'],
                        output_dir
                    )
                elif task_info.status == -1:  # Error status
                    logger.error(f"Export failed: {task_info.message}")
                    return None

            time.sleep(self.config.retry_interval)

        logger.error(f"Export timeout after {max_wait_time} seconds")
        return None
