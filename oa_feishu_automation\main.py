#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Main application for OA to Feishu automation
"""

import sys
import argparse
from pathlib import Path
from typing import Dict, Any

from config.settings import settings, OA_REPORTS
from core.cookie_manager import <PERSON><PERSON><PERSON>anager
from core.oa_client import OAClient
from core.excel_processor import ExcelProcessor
from core.feishu_client import FeishuClient
from utils.logger import setup_logger

class OAFeishuAutomation:
    """
    Main automation class for OA to Feishu data synchronization
    """
    
    def __init__(self):
        """Initialize automation components"""
        self.logger = setup_logger()
        self.cookie_manager = CookieManager()
        self.oa_client = OAClient(self.cookie_manager)
        self.excel_processor = ExcelProcessor()
        self.feishu_client = FeishuClient()

        # Ensure output directories exist
        Path("downloads").mkdir(exist_ok=True)
        Path("logs").mkdir(exist_ok=True)
    
    def validate_setup(self) -> bool:
        """
        Validate that all components are properly configured
        
        Returns:
            True if setup is valid
        """
        self.logger.info("Validating setup...")

        # Validate CookieCloud connection
        if not self.cookie_manager.load_cookies():
            self.logger.error("Failed to load cookies from CookieCloud")
            return False

        # Validate OA cookies
        if not self.cookie_manager.validate_oa_cookies():
            self.logger.error("OA cookies validation failed")
            return False

        # Validate Feishu access
        if not self.feishu_client.get_access_token():
            self.logger.error("Failed to get Feishu access token")
            return False

        self.logger.info("Setup validation completed successfully")
        return True
    
    def get_table_id_for_report(self, report_type: str) -> str:
        """
        Get Feishu table ID for report type
        
        Args:
            report_type: Type of report
            
        Returns:
            Table ID
        """
        if report_type == 'contract':
            return settings.feishu.contract_table_id
        elif report_type in ['payment', 'prepayment']:
            return settings.feishu.payment_table_id
        else:
            raise ValueError(f"Unknown report type: {report_type}")
    
    def process_single_report(self, report_type: str, date_range_days: int = 30) -> Dict[str, Any]:
        """
        Process a single report type
        
        Args:
            report_type: Type of report to process
            date_range_days: Number of days to look back
            
        Returns:
            Processing results
        """
        self.logger.info(f"Processing {report_type} report...")

        results = {
            "report_type": report_type,
            "success": False,
            "downloaded_file": None,
            "processed_records": 0,
            "upload_stats": {}
        }

        try:
            # Step 1: Download Excel file from OA
            excel_file = self.oa_client.full_download_process(
                report_type=report_type,
                date_range_days=date_range_days
            )

            if not excel_file:
                self.logger.error(f"Failed to download {report_type} Excel file")
                return results

            results["downloaded_file"] = str(excel_file)
            self.logger.info(f"Successfully downloaded: {excel_file}")

            # Step 2: Process Excel file
            processed_records = self.excel_processor.process_excel_file(excel_file, report_type)

            if not processed_records:
                self.logger.warning(f"No records processed from {excel_file}")
                return results

            # Validate records
            valid_records = self.excel_processor.validate_records(processed_records, report_type)
            results["processed_records"] = len(valid_records)

            if not valid_records:
                self.logger.warning(f"No valid records found for {report_type}")
                return results

            self.logger.info(f"Processed {len(valid_records)} valid records")

            # Step 3: Upload to Feishu
            table_id = self.get_table_id_for_report(report_type)
            upload_stats = self.feishu_client.incremental_upload(table_id, valid_records)

            results["upload_stats"] = upload_stats
            results["success"] = upload_stats["failed"] == 0

            self.logger.info(f"Upload completed for {report_type}: {upload_stats}")

        except Exception as e:
            self.logger.error(f"Error processing {report_type} report: {e}")
            results["error"] = str(e)
        
        return results
    
    def process_all_reports(self, date_range_days: int = 30) -> Dict[str, Any]:
        """
        Process all report types
        
        Args:
            date_range_days: Number of days to look back
            
        Returns:
            Overall processing results
        """
        self.logger.info("Starting processing of all reports...")

        overall_results = {
            "total_reports": len(OA_REPORTS),
            "successful_reports": 0,
            "failed_reports": 0,
            "report_results": {}
        }

        for report_type in OA_REPORTS.keys():
            self.logger.info(f"\n{'='*50}")
            self.logger.info(f"Processing {report_type.upper()} report")
            self.logger.info(f"{'='*50}")

            result = self.process_single_report(report_type, date_range_days)
            overall_results["report_results"][report_type] = result

            if result["success"]:
                overall_results["successful_reports"] += 1
                self.logger.info(f"✅ {report_type} report processed successfully")
            else:
                overall_results["failed_reports"] += 1
                self.logger.error(f"❌ {report_type} report processing failed")

        self.logger.info(f"\n{'='*50}")
        self.logger.info("OVERALL RESULTS")
        self.logger.info(f"{'='*50}")
        self.logger.info(f"Total reports: {overall_results['total_reports']}")
        self.logger.info(f"Successful: {overall_results['successful_reports']}")
        self.logger.info(f"Failed: {overall_results['failed_reports']}")
        
        return overall_results
    
    def get_feishu_schema_info(self) -> Dict[str, Any]:
        """
        Get Feishu table schema information for debugging
        
        Returns:
            Schema information for all tables
        """
        self.logger.info("Retrieving Feishu table schemas...")

        schemas = {}

        # Contract table schema
        try:
            contract_schema = self.feishu_client.get_table_schema(settings.feishu.contract_table_id)
            schemas["contract"] = contract_schema
        except Exception as e:
            self.logger.error(f"Failed to get contract table schema: {e}")

        # Payment table schema
        try:
            payment_schema = self.feishu_client.get_table_schema(settings.feishu.payment_table_id)
            schemas["payment"] = payment_schema
        except Exception as e:
            self.logger.error(f"Failed to get payment table schema: {e}")
        
        return schemas

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="OA to Feishu Automation")
    parser.add_argument(
        '--report-type',
        choices=['contract', 'payment', 'prepayment', 'all'],
        default='all',
        help='Type of report to process'
    )
    parser.add_argument(
        '--days',
        type=int,
        default=30,
        help='Number of days to look back (default: 30)'
    )
    parser.add_argument(
        '--validate-only',
        action='store_true',
        help='Only validate setup without processing'
    )
    parser.add_argument(
        '--get-schema',
        action='store_true',
        help='Get Feishu table schema information'
    )
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level'
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logger = setup_logger(level=args.log_level)

    try:
        # Initialize automation
        automation = OAFeishuAutomation()
        
        # Validate setup
        if not automation.validate_setup():
            logger.error("Setup validation failed. Please check your configuration.")
            sys.exit(1)
        
        if args.validate_only:
            logger.info("Setup validation completed successfully. Exiting.")
            return
        
        if args.get_schema:
            schemas = automation.get_feishu_schema_info()
            logger.info(f"Schema information retrieved: {schemas}")
            return
        
        # Process reports
        if args.report_type == 'all':
            results = automation.process_all_reports(args.days)
        else:
            results = automation.process_single_report(args.report_type, args.days)
        
        logger.info(f"Processing completed: {results}")
        
    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
