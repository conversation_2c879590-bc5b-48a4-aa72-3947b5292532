#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Feishu (Lark) client for multi-dimensional table operations
"""

import requests
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from ..config.settings import settings
from ..utils.logger import logger

@dataclass
class FeishuTableField:
    """Feishu table field information"""
    field_id: str
    field_name: str
    field_type: int
    description: Optional[str] = None

class FeishuClient:
    """
    Feishu client for multi-dimensional table operations
    """
    
    def __init__(self):
        """Initialize Feishu client"""
        self.config = settings.feishu
        self.access_token = None
        self.token_expires_at = 0
        self.session = requests.Session()
        self.session.timeout = 30
    
    def get_access_token(self) -> Optional[str]:
        """
        Get tenant access token for API authentication
        
        Returns:
            Access token if successful, None if failed
        """
        # Check if current token is still valid
        if self.access_token and time.time() < self.token_expires_at:
            return self.access_token
        
        url = self.config.auth_url
        headers = {
            'Content-Type': 'application/json; charset=utf-8'
        }
        
        data = {
            'app_id': self.config.app_id,
            'app_secret': self.config.app_secret
        }
        
        try:
            logger.info("Requesting Feishu access token...")
            response = self.session.post(url, json=data, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    self.access_token = result['tenant_access_token']
                    # Token expires in 2 hours, refresh 10 minutes early
                    self.token_expires_at = time.time() + result.get('expire', 7200) - 600
                    logger.info("Successfully obtained Feishu access token")
                    return self.access_token
                else:
                    logger.error(f"Failed to get access token: {result}")
                    return None
            else:
                logger.error(f"HTTP error getting access token: {response.status_code}")
                return None
                
        except requests.RequestException as e:
            logger.error(f"Network error getting access token: {e}")
            return None
    
    def _make_api_request(self, method: str, endpoint: str, data: Optional[Dict] = None, 
                         params: Optional[Dict] = None) -> Optional[Dict]:
        """
        Make authenticated API request to Feishu
        
        Args:
            method: HTTP method
            endpoint: API endpoint (relative to base URL)
            data: Request body data
            params: Query parameters
            
        Returns:
            Response data if successful, None if failed
        """
        access_token = self.get_access_token()
        if not access_token:
            logger.error("Failed to get access token for API request")
            return None
        
        url = f"{self.config.base_url}{endpoint}"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json; charset=utf-8'
        }
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, headers=headers, params=params)
            elif method.upper() == 'POST':
                response = self.session.post(url, headers=headers, json=data, params=params)
            elif method.upper() == 'PUT':
                response = self.session.put(url, headers=headers, json=data, params=params)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url, headers=headers, params=params)
            else:
                logger.error(f"Unsupported HTTP method: {method}")
                return None
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    return result.get('data', {})
                else:
                    logger.error(f"API error: {result}")
                    return None
            else:
                logger.error(f"HTTP error {response.status_code}: {response.text}")
                return None
                
        except requests.RequestException as e:
            logger.error(f"Network error in API request: {e}")
            return None
    
    def get_table_fields(self, table_id: str) -> List[FeishuTableField]:
        """
        Get table field definitions
        
        Args:
            table_id: Table ID
            
        Returns:
            List of table fields
        """
        endpoint = f"bitable/v1/apps/{self.config.base_id}/tables/{table_id}/fields"
        
        result = self._make_api_request('GET', endpoint)
        if not result:
            return []
        
        fields = []
        for item in result.get('items', []):
            field = FeishuTableField(
                field_id=item['field_id'],
                field_name=item['field_name'],
                field_type=item['type'],
                description=item.get('description')
            )
            fields.append(field)
        
        logger.info(f"Retrieved {len(fields)} fields for table {table_id}")
        return fields
    
    def get_table_records(self, table_id: str, field_names: Optional[List[str]] = None,
                         page_size: int = 500) -> Dict[str, Dict]:
        """
        Get existing records from table
        
        Args:
            table_id: Table ID
            field_names: Specific field names to retrieve
            page_size: Number of records per page
            
        Returns:
            Dictionary mapping record IDs to record data
        """
        endpoint = f"bitable/v1/apps/{self.config.base_id}/tables/{table_id}/records"
        
        params = {
            'page_size': page_size
        }
        
        if field_names:
            params['field_names'] = field_names
        
        all_records = {}
        page_token = None
        
        while True:
            if page_token:
                params['page_token'] = page_token
            
            result = self._make_api_request('GET', endpoint, params=params)
            if not result:
                break
            
            # Process records
            for item in result.get('items', []):
                record_id = item['record_id']
                all_records[record_id] = item['fields']
            
            # Check for next page
            page_token = result.get('page_token')
            if not result.get('has_more', False):
                break
        
        logger.info(f"Retrieved {len(all_records)} existing records from table {table_id}")
        return all_records
    
    def batch_add_records(self, table_id: str, records: List[Dict[str, Any]], 
                         batch_size: int = 500) -> Tuple[bool, int, List[str]]:
        """
        Batch add records to table
        
        Args:
            table_id: Table ID
            records: List of record data
            batch_size: Number of records per batch
            
        Returns:
            Tuple of (success, added_count, failed_records)
        """
        if not records:
            logger.info("No records to add")
            return True, 0, []
        
        endpoint = f"bitable/v1/apps/{self.config.base_id}/tables/{table_id}/records/batch_create"
        
        total_added = 0
        failed_records = []
        
        # Process records in batches
        for i in range(0, len(records), batch_size):
            batch = records[i:i + batch_size]
            
            # Format records for API
            formatted_records = []
            for record in batch:
                formatted_records.append({'fields': record})
            
            data = {'records': formatted_records}
            
            result = self._make_api_request('POST', endpoint, data)
            if result:
                added_count = len(result.get('records', []))
                total_added += added_count
                logger.info(f"Batch {i//batch_size + 1}: Added {added_count} records")
            else:
                logger.error(f"Failed to add batch {i//batch_size + 1}")
                failed_records.extend([f"batch_{i//batch_size + 1}_record_{j}" for j in range(len(batch))])
        
        success = len(failed_records) == 0
        logger.info(f"Batch add completed: {total_added} added, {len(failed_records)} failed")
        return success, total_added, failed_records

    def update_record(self, table_id: str, record_id: str, fields: Dict[str, Any]) -> bool:
        """
        Update a single record

        Args:
            table_id: Table ID
            record_id: Record ID to update
            fields: Fields to update

        Returns:
            True if successful
        """
        endpoint = f"bitable/v1/apps/{self.config.base_id}/tables/{table_id}/records/{record_id}"

        data = {'fields': fields}

        result = self._make_api_request('PUT', endpoint, data)
        if result:
            logger.debug(f"Successfully updated record {record_id}")
            return True
        else:
            logger.error(f"Failed to update record {record_id}")
            return False

    def find_duplicate_records(self, table_id: str, new_records: List[Dict[str, Any]],
                              key_fields: List[str]) -> Tuple[List[Dict], List[Dict], List[Tuple]]:
        """
        Find duplicate records based on key fields

        Args:
            table_id: Table ID
            new_records: New records to check
            key_fields: Fields to use for duplicate detection

        Returns:
            Tuple of (new_records, existing_records, update_pairs)
        """
        # Get existing records
        existing_records = self.get_table_records(table_id, key_fields)

        # Build lookup dictionary for existing records
        existing_lookup = {}
        for record_id, fields in existing_records.items():
            # Create key from key fields
            key_values = []
            for field in key_fields:
                value = fields.get(field, "")
                key_values.append(str(value).strip())
            key = "|".join(key_values)
            existing_lookup[key] = (record_id, fields)

        # Categorize new records
        records_to_add = []
        records_to_update = []
        update_pairs = []

        for new_record in new_records:
            # Create key for new record
            key_values = []
            for field in key_fields:
                value = new_record.get(field, "")
                key_values.append(str(value).strip())
            key = "|".join(key_values)

            if key in existing_lookup:
                # Record exists, prepare for update
                record_id, existing_fields = existing_lookup[key]
                records_to_update.append(existing_fields)
                update_pairs.append((record_id, new_record))
            else:
                # New record
                records_to_add.append(new_record)

        logger.info(f"Duplicate analysis: {len(records_to_add)} new, {len(update_pairs)} to update")
        return records_to_add, records_to_update, update_pairs

    def incremental_upload(self, table_id: str, records: List[Dict[str, Any]],
                          key_fields: List[str] = None) -> Dict[str, Any]:
        """
        Perform incremental upload with duplicate detection

        Args:
            table_id: Table ID
            records: Records to upload
            key_fields: Fields to use for duplicate detection

        Returns:
            Dictionary with upload statistics
        """
        if not records:
            return {"added": 0, "updated": 0, "failed": 0, "total": 0}

        # Default key fields if not specified
        if not key_fields:
            key_fields = ['申请编号']  # Use application number as default key

        logger.info(f"Starting incremental upload of {len(records)} records to table {table_id}")

        # Find duplicates
        records_to_add, records_to_update, update_pairs = self.find_duplicate_records(
            table_id, records, key_fields
        )

        stats = {
            "total": len(records),
            "added": 0,
            "updated": 0,
            "failed": 0
        }

        # Add new records
        if records_to_add:
            success, added_count, failed_records = self.batch_add_records(table_id, records_to_add)
            stats["added"] = added_count
            stats["failed"] += len(failed_records)

        # Update existing records
        if update_pairs:
            updated_count = 0
            for record_id, new_fields in update_pairs:
                if self.update_record(table_id, record_id, new_fields):
                    updated_count += 1
                else:
                    stats["failed"] += 1
            stats["updated"] = updated_count

        logger.info(f"Incremental upload completed: {stats}")
        return stats

    def get_table_schema(self, table_id: str) -> Dict[str, Any]:
        """
        Get complete table schema information

        Args:
            table_id: Table ID

        Returns:
            Dictionary with table schema information
        """
        # Get table info
        table_endpoint = f"bitable/v1/apps/{self.config.base_id}/tables/{table_id}"
        table_info = self._make_api_request('GET', table_endpoint)

        # Get field definitions
        fields = self.get_table_fields(table_id)

        schema = {
            "table_info": table_info,
            "fields": [
                {
                    "field_id": field.field_id,
                    "field_name": field.field_name,
                    "field_type": field.field_type,
                    "description": field.description
                }
                for field in fields
            ]
        }

        logger.info(f"Retrieved schema for table {table_id}: {len(fields)} fields")
        return schema
